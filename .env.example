# Environment Variables for Vercel Deployment
# Copy this file to .env.local for local development
# Add these variables to your Vercel project settings

# Gemini API Key (if using AI features)
GEMINI_API_KEY=your_gemini_api_key_here

# Firebase Configuration (these are already in firebase.config.ts but can be moved to env vars for security)
# VITE_FIREBASE_API_KEY=your_firebase_api_key
# VITE_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
# VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
# VITE_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
# VITE_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
# VITE_FIREBASE_APP_ID=your_firebase_app_id
# VITE_FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id
